/*
 * Input Area Styles (migrated from ziantrix-style-clean.css)
 * Includes chat input containers, forms, buttons, file upload areas, and responsive states.
 * All input area styles are now consolidated here for maintainability.
 */
/* Input Attachments Styling */
/* Apply consistent font family */
.input-attachments-container,
.input-attachment,
.input-attachment .file-name,
.input-attachment .file-type {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}
.input-attachments-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px;
    background-color: var(--input-bg);
    border-radius: 8px;
    max-width: 100%;
    width: 100%;
    overflow-x: auto;
    scrollbar-width: none; /* Hide scrollbar for Firefox */
    -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
    margin-bottom: 4px;
    border: 1px solid var(--border-color);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.input-attachments-container::-webkit-scrollbar {
    display: none;
}

.input-attachments-container:empty {
    display: none;
    border: none;
    padding: 0;
    margin: 0;
}

.input-attachment {
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 6px 8px;
    width: auto;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    margin-bottom: 0;
    flex-shrink: 0;
}

.input-attachment .file-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    color: white;
    font-size: 12px;
    flex-shrink: 0;
}

.input-attachment .file-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.input-attachment .file-name {
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0;
    color: var(--text-primary);
}

.input-attachment .file-type {
    font-size: 0.625rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 400;
}

.input-attachment .remove-attachment {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 2px;
    margin-left: 4px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.input-attachment .remove-attachment:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
}

/* Dark theme adjustments */
.theme-dark .input-attachments-container {
    border-color: var(--border-color);
}

.theme-dark .input-attachment {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.theme-dark .input-attachment .remove-attachment:hover {
    background-color: rgba(255, 107, 107, 0.15);
    color: var(--cta-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .input-attachments-container {
        padding: 6px;
    }

    .input-attachment {
        max-width: 200px;
    }
}

/* --- Chat Input Area Styles (migrated from ziantrix-style-clean.css) --- */

/* Removed background, box-shadow, and border from .chat-input-container as per user request. */
.chat-input-container {
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    height: 43.2px !important;
    min-height: 43.2px !important;
    max-width: 660px !important;
    width: 88vw !important;
    padding: 0 4px !important;
    border-radius: 8px !important;
    margin: 0 auto !important;
}
@media (max-width: 768px) {
  .chat-input-container {
    padding: 0 2vw 12px 2vw;
  }
}
@media (max-width: 576px) {
  .chat-input-container {
    padding: 0 1vw 8px 1vw;
  }
}

.chat-input-form {
    width: 100% !important;
    max-width: 648px !important;
    min-width: 0;
    box-sizing: border-box;
    margin: 0 auto;
    background: var(--input-bg);
    border-radius: 12px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    border: 1px solid var(--input-border);
    overflow: hidden;
    display: flex;
    flex-direction: row;
}

.input-wrapper {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    flex: 1 1 auto !important;
}

textarea, #userInput {
    min-height: 25.2px !important;
    max-height: 28.8px !important;
    height: 25.2px !important;
    padding: 0 12px 0 0 !important;
    font-size: 1rem !important;
    line-height: 25.2px !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    color: #222 !important;
    resize: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    flex: 1 1 auto !important;
    display: block !important;
}

.input-actions-row {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-end !important;
    justify-content: space-between !important;
    padding: 0 !important;
    margin: 0 !important;
    gap: 0 !important;
}

.input-actions-left {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-end !important;
    gap: 8px !important;
}

.input-action-btn, .action-btn {
    width: 22px !important;
    height: 22px !important;
    min-width: 22px !important;
    min-height: 22px !important;
    padding: 0 !important;
    margin: 0 4px 0 0 !important;
    font-size: 18px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 4px !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    transition: background 0.2s;
}

.send-btn {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
    min-height: 32px !important;
    margin: 0 0 0 8px !important;
    background: #333 !important;
    color: #fff !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 8px !important;
}

.input-horizontal-row {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  width: 100% !important;
  padding: 8px 12px !important;
}

.input-horizontal-row .left-actions {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  flex-shrink: 0 !important;
}

.input-horizontal-row .input-main-area {
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
}

.input-horizontal-row .input-main-area textarea {
  width: calc(100% + 0.5cm) !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  resize: none !important;
  font-size: 1rem !important;
  padding: 8px 12px !important;
  min-height: 20px !important;
  max-height: 80px !important;
  line-height: 1.4 !important;
}

.input-horizontal-row .right-actions {
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}

@media (max-width: 768px) {
  .input-horizontal-row {
    padding: 6px 8px !important;
    gap: 6px !important;
  }
  .input-horizontal-row .left-actions {
    gap: 4px !important;
  }
  .input-horizontal-row .input-main-area textarea {
    font-size: 0.95rem !important;
    padding: 6px 8px !important;
  }
  .action-btn {
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    min-height: 20px !important;
    font-size: 16px !important;
  }
  .send-btn {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    min-height: 28px !important;
  }
}

@media (max-width: 480px) {
  .input-horizontal-row {
    padding: 4px 6px !important;
    gap: 4px !important;
  }
  .input-horizontal-row .input-main-area textarea {
    font-size: 0.9rem !important;
    padding: 4px 6px !important;
  }
  .action-btn {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    min-height: 18px !important;
    font-size: 14px !important;
    margin: 0 2px 0 0 !important;
  }
  .send-btn {
    width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
    min-height: 26px !important;
    margin: 0 0 0 4px !important;
  }
}
